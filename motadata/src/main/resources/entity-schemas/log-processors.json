{"entity": "Log Processor", "table": "tbl_log_processor", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.processor.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.processor.type", "title": "Name", "type": "string", "rules": ["required"], "values": ["<PERSON><PERSON><PERSON>", "Remapper", "<PERSON><PERSON>", "Arithmetic/String Processor"]}], "entries": [{"type": "inline", "records": [{"log.processor.name": "Regex Parser Testing", "log.processor.type": "<PERSON><PERSON><PERSON>", "plugin.id": "700000", "log.processor.sample.log": "<85> AIOps sudo: motadata : PWD=/home/<USER>/usr/bin/lsof -p 1333", "log.processor.rule": "(?:<(?<priority>\\d+)>)?(?<timestamp>(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\\s.*\\d+\\s+\\d+:\\d+:\\d+)\\s(?<logsource>[a-zA-Z0-9\\-#\\.\\(\\)\\/%&\\s]{1,})\\s(?<program>[\\w\\-]*)(?:[\\[\\-\\/:(](?<pid>\\d+)[\\]\\-:)\\/])?(?:[\\[\\-:(])?(?<message>.*)", "id": 10000000000001}], "version": "2.3"}]}