{"entity": "Log Index", "table": "tbl_log_index", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.index.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.index.retention.period", "title": "Name", "type": "string"}, {"name": "log.index.status", "title": "Name", "type": "string"}], "entries": [{"type": "inline", "records": [{"log.index.name": "Uno", "log.index.retention.period": "-15d", "log.index.retention.filter": {"operator": "and", "filter": "include", "groups": [{"filter": "include", "operator": "and", "conditions": [{"operand": "event.source.type", "operator": "in", "value": ["Linux"]}]}]}, "log.index.status": "yes", "id": 10000000000001}], "version": "1.5"}]}