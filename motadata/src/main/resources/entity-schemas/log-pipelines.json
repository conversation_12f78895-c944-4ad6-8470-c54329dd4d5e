{"entity": "Log Pipeline", "table": "tbl_log_pipelines", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "log.pipeline.name", "title": "Name", "type": "string", "rules": ["required", "unique"]}, {"name": "log.pipeline.description", "title": "Name", "type": "string"}, {"name": "log.pipeline.status", "title": "Name", "type": "string"}, {"name": "log.pipeline.processors", "title": "Name", "type": "List"}], "entries": [{"type": "inline", "records": [{"log.pipeline.name": "pipeline1", "log.pipeline.description": "System Pipeline", "log.pipeline.filter": {}, "log.pipeline.processors": [10000000000001], "log.pipeline.status": "yes", "id": 10000000000001}], "version": "1.5"}]}