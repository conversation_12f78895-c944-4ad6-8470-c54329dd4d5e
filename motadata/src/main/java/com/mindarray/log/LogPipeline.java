/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */


package com.mindarray.log;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.ai.AIConstants;
import com.mindarray.api.LogIndex;
import com.mindarray.api.LogProcessor;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.db.DBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.job.JobScheduler;
import com.mindarray.policy.PolicyEngine;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.*;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.*;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Pattern;

import static com.mindarray.ErrorMessageConstants.LOG_MESSAGE_TOO_BIG_ERROR;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.LogParser.*;
import static com.mindarray.api.LogPipeline.LOG_PIPELINE_PROCESSORS;
import static com.mindarray.api.LogProcessor.*;
import static com.mindarray.datastore.DatastoreConstants.INDEXABLE_COLUMNS;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogEngineConstants.*;
import static com.mindarray.log.LogEngineConstants.LogParserType.CUSTOM;
import static com.mindarray.log.LogEngineConstants.LogParserType.valueOfName;
import static com.mindarray.log.LogEngineConstants.LogProcessorType.valueOfProcessor;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ID;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_NAME;

/**
 * A verticle responsible for parsing log data from various sources using different parsing strategies.
 * <p>
 * The LogParser is a central component of the log processing system that handles the parsing
 * of log data from various sources. It supports multiple parsing strategies:
 * <ul>
 *   <li>Regex-based parsing - Uses regular expressions to extract fields from log messages</li>
 *   <li>JSON parsing - Parses structured JSON log messages</li>
 *   <li>Delimiter-based parsing - Splits log messages using a specified delimiter</li>
 *   <li>Custom plugin-based parsing - Uses custom plugins for specialized log formats</li>
 * </ul>
 * <p>
 * Key features:
 * <ul>
 *   <li>Maintains maps of parsers, patterns, and other metadata</li>
 *   <li>Filters logs based on keywords and source</li>
 *   <li>Parses logs using different strategies</li>
 *   <li>Enriches log data with additional information</li>
 *   <li>Handles the loading and compilation of parser plugins</li>
 *   <li>Flushes parsed logs to storage</li>
 * </ul>
 * <p>
 * The LogParser integrates with the Vert.x event bus system for communication with other components.
 */
public class LogPipeline extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogPipeline.class, MOTADATA_LOG, "Log Pipeline");

    /**
     * Maximum size of log messages in bytes
     */
    private static final int MAX_LOG_SIZE_BYTES = MotadataConfigUtil.getMaxLogSizeBytes();

    /**
     * Set of metadata field names that are handled specially during parsing
     */
    private static final Set<String> METADATA_FIELDS = Set.of(TIME_STAMP, EVENT_TIMESTAMP, EVENT_SOURCE, EVENT_CATEGORY, MESSAGE, EVENT_PATTERN_ID, RECEIVED_TIMESTAMP, EVENT_VOLUME_BYTES, EVENT);

    private static final Pattern FIELD_PATTERN = Pattern.compile("[^a-zA-Z0-9]");

    /**
     * JsonObject for storing enriched event data during parsing
     */
    private final JsonObject enrichedEvent = new JsonObject();

    /**
     * Map of parsers indexed by their IDs
     */
    private final Map<Long, JsonObject> pipes = new HashMap<>();
    private final Map<Long, JsonObject> processors = new HashMap<>();

    /**
     * Map of parser IDs indexed by source
     */
    private final Map<String, List<Long>> parsersBySource = new HashMap<>();

    /**
     * Map of timezones indexed by source
     */
    private final Map<String, String> timezonesBySource = new HashMap<>();
    private final Map<Long, Pattern> patternsByProcessor = new HashMap<>();

    /**
     * Map of condition keywords indexed by parser ID
     */
    private final Map<Long, List<String>> conditionKeywords = new HashMap<>();

    /**
     * Map of compiled regex patterns indexed by parser ID
     */
    private final Map<Long, Pattern> patterns = new HashMap<>();

    /**
     * Map of date/time formatters indexed by parser ID
     */
    private final Map<Long, String> dateTimeFormatters = new HashMap<>();

    /**
     * Map of parser plugins indexed by parser ID
     */
    private final Map<Long, Plugin> plugins = new HashMap<>();

    /**
     * Map of for storing stats
     */
    private final Map<String, long[]> stats = new HashMap<>();

    /** Sets for filtering logs */
    /**
     * Set of registered sources
     */
    private final Set<String> sources = new HashSet<>();

    /**
     * Set of registered categories
     */
    private final Set<String> categories = new HashSet<>();

    /**
     * StringBuilder for building strings efficiently
     */
    private final StringBuilder builder = new StringBuilder(0);

    /**
     * Map of enriched field names for caching
     */
    private final Map<String, String> enrichedFields = new HashMap<>();

    /**
     * JsonObject for storing indexable columns configuration
     */
    private JsonObject indexableColumns;

    /**
     * Event engine for handling log events
     */
    private EventEngine eventEngine;

    /**
     * Bloom filter for caching IP addresses that couldn't be resolved
     */
    private BloomFilter<String> garbageIPAddresses;

    /**
     * Set of column mappers
     */
    private Set<String> mappers;


    private static final int MAX_LOGS_PER_IP = 1000;
    private final Map<String, Deque<String>> logMap = new HashMap<>();
    private static final String LOG_DIR = CURRENT_DIR + PATH_SEPARATOR + "log-pipeline";
    private static final int RAW_LOGS_TIMER = 120; //in sec
    private static  long timer = 60; //in sec
    private static Map<String, Coordinate> countryCoords = new HashMap<>();

    /**
     * Policy engine for handling event qualification and aggregation
     */
    private final PolicyEngine policyEngine;

    /**
     * Constructor initializes the policy engine for log events
     */
    public LogPipeline()
    {
        this.policyEngine = new PolicyEngine(EVENT_LOG);
    }


    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            garbageIPAddresses = BloomFilter.create(Funnels.stringFunnel(StandardCharsets.UTF_8), 5000000, 0.005);   //wrapper to reduce GeoDBUtil Hits

            loadCountriesCsv(CURRENT_DIR + PATH_SEPARATOR + "countries.csv");

            var items = LogPipelineConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                pipes.put(item.getLong(ID),item);
            }

            items = LogProcessorConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                processors.put(item.getLong(ID),item);

                patternsByProcessor.put(item.getLong(ID), Pattern.compile(item.getString(LOG_PROCESSOR_RULE)));
            }

            var eventSources = EventSourceConfigStore.getStore().getItemsByMultiValueField(EVENT_TYPE, EVENT_LOG);

            for (var index = 0; index < eventSources.size(); index++)
            {
                var item = eventSources.getJsonObject(index);

                var source = item.getString(EVENT_SOURCE);

                if (item.containsKey(PLUGIN_ID))
                {
                    for (var pluginId : item.getJsonArray(PLUGIN_ID))
                    {
                        sources.add(source + DASH_SEPARATOR + pluginId);
                    }
                }
            }

            if (Bootstrap.bootstrapType() == BootstrapType.APP)
            {
                mappers = new HashSet<>();

                indexableColumns = new JsonObject();

                loadIndexableColumns();

                vertx.eventBus().<JsonObject>localConsumer(EVENT_INDEXABLE_COLUMN_UPDATE, message ->
                {
                    var event = message.body();

                    try
                    {
                        if (!event.isEmpty())
                        {
                            for (var entry : event.getJsonObject(EVENT_CONTEXT).getMap().entrySet())
                            {
                                indexableColumns.getMap().computeIfAbsent(entry.getKey(), value -> new JsonArray());

                                for (var column : JsonObject.mapFrom(entry.getValue()).getMap().entrySet())
                                {
                                    if (!indexableColumns.getJsonArray(entry.getKey()).contains(column.getKey()))
                                    {
                                        indexableColumns.getJsonArray(entry.getKey()).add(column.getKey());
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception exception)
                    {
                        message.fail(GlobalConstants.NOT_AVAILABLE, exception.getMessage());

                        LOGGER.error(exception);
                    }
                }).exceptionHandler(LOGGER::error);
            }

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                var id = event.getLong(ID);

                switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_LOG_PIPELINE_PROCESSOR,UPDATE_LOG_PIPELINE_PROCESSOR -> {

                        var item = LogProcessorConfigStore.getStore().getItem(id);

                        processors.put(item.getLong(ID), item);

                        patternsByProcessor.put(item.getLong(ID), Pattern.compile(item.getString(LOG_PROCESSOR_RULE)));
                    }

                    case DELETE_LOG_PIPELINE_PROCESSOR -> {

                        processors.remove(id);

                        patternsByProcessor.remove(id);
                    }

                    case ADD_LOG_PIPELINE, UPDATE_LOG_PIPELINE -> {

                        var item = LogPipelineConfigStore.getStore().getItem(id);

                        pipes.put(item.getLong(ID), item);
                    }

                    case DELETE_LOG_PIPELINE -> pipes.remove(id);

                    case ADD_POLICY, UPDATE_POLICY ->
                    {
                        var policy = EventPolicyConfigStore.getStore().getItem(event.getLong(ID));

                        if(policy!= null && policyEngine.isApplicablePolicy(policy))
                        {
                            policyEngine.addPolicy(policy);
                        }
                    }

                    case DELETE_POLICY ->
                    {
                        policyEngine.removePolicy(event.getLong(ID));
                    }

                    case SUPPRESS_POLICY ->
                    {
                        if (message.body() != null)
                        {
                            policyEngine.suppressPolicy(event.getLong(POLICY_ID), true);
                        }
                    }

                    case UNSUPPRESS_POLICY ->
                    {
                        if (message.body() != null)
                        {
                            policyEngine.suppressPolicy(event.getLong(POLICY_ID), false);
                        }
                    }

                    case DISABLE_POLICY_ACTION_TRIGGER ->
                    {
                        var item = SchedulerConfigStore.getStore().getItem(event.getLong(ID));

                        if (item != null)
                        {
                            Bootstrap.configDBService().delete(DBConstants.TBL_SCHEDULER, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, event.getLong(ID)), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, future ->
                            {
                                if (future.succeeded())
                                {
                                    SchedulerConfigStore.getStore().deleteItem(event.getLong(ID));

                                    JobScheduler.removeJob(event.getLong(ID));
                                }
                            });
                        }
                    }

                    default ->
                    {
                        // do nothing
                    }
                }
            }).exceptionHandler(LOGGER::error);

            //broadcasting stats to stat calculator
            vertx.setPeriodic(60 * 1000, timer ->
            {

                vertx.eventBus().send(EVENT_LOG_STAT, stats);

                stats.clear();
            });


            eventEngine = new EventEngine().setLogger(LOGGER).setEventType(config().getString(EVENT_TYPE)).setPersistEventOffset(true).setEventHandler(this::filter).start(vertx, promise);

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("For Bootstrap type: %s", Bootstrap.bootstrapType()));
            }

            vertx.setPeriodic(RAW_LOGS_TIMER * 1000, id -> {

                if (!logMap.isEmpty())
                {
                    dumpLogsToFile();

                    vertx.cancelTimer(timer);

                    timer = vertx.setPeriodic(10000, handler ->{

                        if(vertx.fileSystem().existsBlocking(CURRENT_DIR + PATH_SEPARATOR + "unique-logs" + PATH_SEPARATOR + "unique-logs.json"))
                        {
                            vertx.cancelTimer(timer);
                            var s = vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + "unique-logs" + PATH_SEPARATOR + "unique-logs.json");
                            var file = new JsonObject(s);
                            LOGGER.info(file);
                            vertx.fileSystem().deleteBlocking(CURRENT_DIR + PATH_SEPARATOR + "unique-logs" + PATH_SEPARATOR + "unique-logs.json");

                            AIConstants.generateLogParser(file).onComplete(voidResult->
                            {
                                try
                                {
                                    var context = new JsonObject().put(LOG_PROCESSOR_TYPE, LogProcessorType.PARSE.getName())
                                            .put(LogProcessor.LOG_PROCESSOR_NAME, EMPTY_VALUE)
                                            .put(LOG_PROCESSOR_RULE, EMPTY_VALUE);

                                    var result = voidResult.result();

                                    if (!result.isEmpty())
                                    {
                                        var futures = new ArrayList<Future<Long>>();

                                        for (var logParser : result.getMap().entrySet())
                                        {
                                            var log = result.getJsonArray(logParser.getKey());

                                            for (var index = 0; index < log.size(); index++)
                                            {
                                                var future = Promise.<Long>promise();

                                                futures.add(future.future());

                                                var logLine = log.getJsonObject(index);

                                                context.put(LOG_PROCESSOR_NAME, logLine.getString("logType"));

                                                context.put(LOG_PROCESSOR_RULE, logLine.getString("regex"));

                                                Bootstrap.configDBService().save(DBConstants.TBL_LOG_PROCESSOR, context.copy(), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        LogProcessorConfigStore.getStore().addItem(asyncResult.result());

                                                        future.complete(asyncResult.result());
                                                    }
                                                });
                                            }
                                        }

                                        Future.join(futures).onComplete(asyncResult ->
                                        {
                                            var ids = LogPipelineConfigStore.getStore().getItem(10000000000001L).getJsonArray(LOG_PIPELINE_PROCESSORS);

                                            for (var index = 0; index < asyncResult.result().size(); index++)
                                            {
                                                var a = asyncResult.result().<Long>resultAt(index);

                                                if (a != null)
                                                {
                                                    ids.add(a);
                                                }
                                            }

                                            Bootstrap.configDBService().update(DBConstants.TBL_LOG_PIPELINE, new JsonObject().put(DBConstants.FIELD_NAME, ID).put(VALUE, 10000000000001L), new JsonObject().put(LOG_PIPELINE_PROCESSORS, ids), DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, res ->
                                            {
                                                if (res.succeeded())
                                                {
                                                    LogPipelineConfigStore.getStore().updateItem(10000000000001L);
                                                }
                                            });
                                        });
                                    }
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);
                                }
                            });
                        }
                    });
                }
            });

            vertx.eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, reply ->
            {
                try
                {
                    var item = reply.result();
                    if(item != null && item.body() != null)
                    {
                        var eventColumns = item.body().getJsonObject(DatastoreConstants.EVENT_COLUMNS);
                        var policies = EventPolicyConfigStore.getStore().getItems();

                        // Initialize the policy engine with event columns and policies
                        policyEngine.initialize(eventColumns, policies);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_COLUMN_MAPPER_UPDATE, message ->
            {
                try
                {
                    var event = message.body();

                    var tokens = event.getString(DatastoreConstants.MAPPER).split(GlobalConstants.COLUMN_SEPARATOR, -1);

                    if (event.getString(CHANGE_NOTIFICATION_TYPE).equalsIgnoreCase(EventBusConstants.ChangeNotificationType.UPDATE_EVENT_COLUMN.name()))
                    {
                        policyEngine.updateEventColumns(tokens);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EVENT_EVENT_POLICY_SUPPRESS, message ->
                    message.reply(message.body() != null && policyEngine.isPolicySuppressed(message.body().getLong(POLICY_ID))));

            // Registers a local consumer to handle policy stats fetch requests.
            // This is triggered by the EventPolicyInspector to collect current group statistics
            // for a given policy ID. The collected stats are processed and sent back via event bus.
            vertx.eventBus().<Long>localConsumer(EVENT_EVENT_POLICY_AGGREGATOR_QUERY + DOT_SEPARATOR + EVENT_LOG, message ->
            {
                try
                {
                    var id = message.body();

                    var item = new JsonObject().put(ID, id).put(RESULT, policyEngine.getPolicyStats(id)); //taking variable for logging purpose...

                    message.reply(item);

                    if(CommonUtil.debugEnabled())
                    {
                        LOGGER.debug(EventPolicyConfigStore.getStore().getItem(id).getString(POLICY_NAME) + " :  " + item);
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            });

            try
            {
                var dirPath = Paths.get(LOG_DIR);

                Files.createDirectories(dirPath); // This will create the directory and any parent dirs if not present
            }
            catch (Exception e)
            {
                LOGGER.error(e);
            }
            promise.future().onComplete(result -> LOGGER.debug(config().getString(EVENT_TYPE) + " started successfully!!!"));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    private void dumpLogsToFile()
    {

        var filename = LOG_DIR + PATH_SEPARATOR + "raw-logs";

        if(vertx.fileSystem().existsBlocking(filename))
        {
            vertx.fileSystem().deleteBlocking(filename);
        }
        try (var writer = new PrintWriter(new FileWriter(filename)))
        {
            for (var entry : logMap.entrySet())
            {
                var ip = entry.getKey();

                for (var log : entry.getValue())
                {
                    writer.println(ip + VALUE_SEPARATOR + log);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
    /**
     * Filters and processes log events based on their source and content.
     * <p>
     * This method is the entry point for log event processing. It:
     * <ul>
     *   <li>Determines if the log should be processed locally or forwarded</li>
     *   <li>Identifies the appropriate parsers for the log based on its source</li>
     *   <li>Filters parsers based on condition keywords</li>
     *   <li>Initiates the parsing process for qualified parsers</li>
     * </ul>
     * <p>
     * If the log comes from a remote event processor, it's flushed directly.
     * Otherwise, the method identifies qualified parsers and initiates parsing.
     *
     * @param event The log event to be filtered and processed
     */
    private void filter(JsonObject event)
    {
        try
        {
            event.remove(EVENT_COPY_REQUIRED);

            var source = event.getString(EVENT_SOURCE);

            event.put(EVENT_SOURCE, source);

            if (CommonUtil.isNotNullOrEmpty(source))
            {
                var defaultLog = LogPatternDetector.detectTimePattern(event.getString(EVENT));

                event.mergeIn(defaultLog);

                //event.put(EVENT,event.getString(EVENT).replace(event.getString(TIME_STAMP),"").trim());

                var pipelines = qualifiedPipeline(event);

                if(!pipelines.isEmpty())
                {
                    parse(event, pipelines);
                }
                else
                {
                    // parser is not assigned, collecting data and will send UNIQUE logs to AI.
                    processOther(event);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private JsonArray qualifiedPipeline(JsonObject event)
    {
        var result = new JsonArray();

        var items = LogPipelineConfigStore.getStore().getItems();

        for(var index = 0; index < items.size(); index++)
        {
            var item = items.getJsonObject(index);

            var filter = item.getJsonObject("log.pipeline.filter");

            if(filter.isEmpty())
            {
                result.add(item.getLong(ID));
            }
            else
            {
                if(evaluateFilter(filter,event))
                {
                    result.add(item.getLong(ID));
                }
            }
        }

        return result;
    }


    public void processOther(JsonObject event)
    {
        var ip = event.getString(EVENT_SOURCE);

        var log = event.getString(EVENT);

        var queue = logMap.computeIfAbsent(ip, k -> new LinkedList<>());

        if (queue.size() == MAX_LOGS_PER_IP)
        {
            queue.removeFirst();
        }

        queue.addLast(log);

        enrichedEvent.clear()
                .put(EVENT_SOURCE, event.getString(EVENT_SOURCE))
                .put(RECEIVED_TIMESTAMP, event.getLong(RECEIVED_TIMESTAMP))
                .put(MESSAGE, event.getString(EVENT))
                .put(EVENT_CATEGORY, Type.OTHER.getName())
                .put(PLUGIN_ID, DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName())
                .put(EVENT_SOURCE_TYPE, Type.OTHER.getName())
                .put(EVENT_TIMESTAMP, event.getLong(TIME_STAMP))
                .put(EVENT_VOLUME_BYTES, event.getInteger(EVENT_VOLUME_BYTES))
                .put(EVENT, event.getString(EVENT));

        flush();
    }

    /**
     * Parses a log event using the qualified parsers.
     * <p>
     * This method attempts to parse a log event using the parsers that have been
     * identified as qualified for this event. It:
     * <ul>
     *   <li>Tries each qualified parser in sequence until one successfully parses the log</li>
     *   <li>Enriches the parsed event with additional metadata</li>
     *   <li>Sets the event timestamp based on the parser's configuration</li>
     *   <li>Flushes the parsed event to storage</li>
     * </ul>
     * <p>
     * If none of the qualified parsers can parse the log, it's categorized as "Other"
     * and still flushed to storage with basic metadata.
     *
     * @param event            The log event to be parsed
     * @param qualifiedPipes Array of parser IDs that are qualified to parse this event
     *                         (empty array indicates the log should be categorized as "Other")
     */
    private void parse(JsonObject event, JsonArray qualifiedPipes)
    {
        try
        {
            var parsed = false;

            if (qualifiedPipes != null && !qualifiedPipes.isEmpty())
            {
                for (var index = 0; index < qualifiedPipes.size(); index++)
                {
                    var pipe = pipes.get(qualifiedPipes.getLong(index));

                    enrichedEvent.clear().put(EVENT_SOURCE, event.getString(EVENT_SOURCE)).put(RECEIVED_TIMESTAMP, event.getLong(RECEIVED_TIMESTAMP)).put(MESSAGE, event.getString(EVENT)).put(EVENT, event.getString(EVENT));

                    parse(event, pipe);

                    if (CommonUtil.isNullOrEmpty(enrichedEvent.getString(EVENT_CATEGORY)))
                    {
                        enrichedEvent.put(EVENT_CATEGORY, pipe.getString(LOG_PARSER_NAME));
                    }

                    enrichedEvent.put(EVENT_SOURCE_TYPE, pipe.getString(LOG_PARSER_SOURCE_TYPE)).put(PLUGIN_ID, pipe.getInteger(PLUGIN_ID));

                    setEventTime(pipe);

                    indexLog();

                    flush();

                    parsed = true;

                }
            }

            if (!parsed)       // other
            {
                enrichedEvent.clear().put(EVENT_SOURCE, event.getString(EVENT_SOURCE)).put(RECEIVED_TIMESTAMP, event.getLong(RECEIVED_TIMESTAMP)).put(MESSAGE, event.getString(EVENT)).put(EVENT_CATEGORY, Type.OTHER.getName()).put(PLUGIN_ID, DatastoreConstants.PluginId.UNKNOWN_LOG_EVENT.getName()).put(EVENT_SOURCE_TYPE, Type.OTHER.getName()).put(EVENT_TIMESTAMP, enrichedEvent.getLong(RECEIVED_TIMESTAMP)).put(EVENT_VOLUME_BYTES, event.getInteger(EVENT_VOLUME_BYTES)).put(EVENT, event.getString(EVENT));

                flush();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        finally
        {
            enrichedEvent.clear();
        }
    }

    private void indexLog()
    {
        var indexers = LogIndexConfigStore.getStore().getItems();

        if(indexers != null && !indexers.isEmpty())
        {
            for(var i=0;i<indexers.size();i++)
            {
                var indexer = indexers.getJsonObject(i);

                if (indexer.getString(LogIndex.LOG_INDEX_STATUS).equalsIgnoreCase(YES))
                {
                    var filter = indexer.getJsonObject(LogIndex.LOG_INDEX_RETENTION_FILTER,null);

                    var groupConditionSatisfied = true;

                    if (filter != null && !filter.isEmpty())
                    {
                        var groupOperator = filter.getString(OPERATOR);

                        var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

                        for (var index = 0; index < conditionGroups.size(); index++)
                        {
                            var conditionGroup = conditionGroups.getJsonObject(index);

                            var operator = conditionGroup.getString(OPERATOR);

                            var conditions = conditionGroup.getJsonArray(CONDITIONS);

                            var satisfied = false;

                            for (var j = 0; j < conditions.size(); j++)
                            {
                                var condition = conditions.getJsonObject(j);

                                var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ? condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] : condition.getString(OPERAND);

                                if (enrichedEvent.containsKey(operand))
                                {
                                    satisfied = PolicyEngineConstants.evaluateCondition(conditionGroup.getString(FILTER).equalsIgnoreCase("include"), condition.getString(OPERATOR), condition.getValue(VALUE), enrichedEvent.getValue(operand));

                                    if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) || (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                                    {
                                        break;
                                    }
                                }
                            }

                            if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
                            {
                                //if main condition is "AND" and any counter group condition is not true so breaking for loop as in "AND" condition all condition is needed to be true
                                groupConditionSatisfied = false;

                                break;
                            }
                            else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                            {
                                //if main condition is "OR" and any counter group condition is true so breaking for loop as in "OR" condition only one condition needed to be true
                                groupConditionSatisfied = true;

                                break;
                            }
                            else if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
                            {
                                groupConditionSatisfied = false;
                            }
                        }

                        if (groupConditionSatisfied)
                        {
                           //send to DB write data

                            flush();

                            break;
                        }
                    }
                }

            }
        }
    }

    /**
     * #internal, parse log as per category selected
     */
    private void parse(JsonObject event, JsonObject pipe)
    {
        var processors = pipe.getJsonArray(LOG_PIPELINE_PROCESSORS);

        var log = event.getString(EVENT);

        if(!processors.isEmpty())
        {
            for(var processorId : processors)
            {
                var processor = LogProcessorConfigStore.getStore().getItem(CommonUtil.getLong(processorId));

                if(processor != null && processor.containsKey(LOG_PROCESSOR_TYPE))
                {
                    switch (valueOfProcessor(processor.getString(LOG_PROCESSOR_TYPE)))
                    {
//                        case PARSE -> LogPatternDetector.parseLogByPattern(log, patternsByProcessor.get(processor.getLong(ID)));
                        case PARSE -> parseLogByPattern(processor, log);

                        case ENRICH_KEY -> enrichKey(processor, log);

                        case ENRICH_VALUE -> enrichValue(processor, log);

                        case ARITHMETIC -> arithmetic(processor, log);
                    };
                }

            }
        }
    }

    private void arithmetic(JsonObject processor, String log) {

    }

    private void enrichValue(JsonObject processor, String log) {

    }

    private void enrichKey(JsonObject processor, String log) {


    }

    private void flush()
    {
        flush(enrichedEvent);

        enrichedEvent.clear();
    }

    /**
     * Flushes a parsed log event to storage and performs additional processing.
     * <p>
     * This method is responsible for the final processing and storage of parsed log events.
     * It performs several important tasks:
     * <ul>
     *   <li>Validates the event timestamp (drops events with timestamps before January 1, 2020)</li>
     *   <li>Trims message length to prevent oversized log entries</li>
     *   <li>Registers the source IP and category in the event source configuration store</li>
     *   <li>Writes the parsed log to the database</li>
     *   <li>Forwards the log to other components as needed (policy engine, statistics, forwarders)</li>
     * </ul>
     * <p>
     * The method behaves differently based on the bootstrap type:
     * <ul>
     *   <li>For EVENT_COLLECTOR or EVENT_PROCESSOR: Sends the event to the remote event processor</li>
     *   <li>For other types: Processes the event locally and writes it to storage</li>
     * </ul>
     *
     * @param event The parsed log event to be flushed to storage
     */
    private void flush(JsonObject event)
    {
        if (!CommonUtil.isNotNullOrEmpty(CommonUtil.getString(event.remove(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID)))) //logs are processed in master
        {
            event.put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY).replaceAll(DASH_SEPARATOR, " "));

            var message = event.getString(MESSAGE);

            if (message.length() > MAX_LOG_SIZE_BYTES)
            {
                message = message.substring(0, MAX_LOG_SIZE_BYTES);

                event.put(MESSAGE, message);

                if (CommonUtil.traceEnabled())
                {

                    LOGGER.trace(String.format(LOG_MESSAGE_TOO_BIG_ERROR, message, MAX_LOG_SIZE_BYTES));
                }

            }

            if (event.getLong(EVENT_TIMESTAMP) < 1577836800L) // Wednesday, January 1, 2020 5:30:00 AM GMT+05:30
            {
                LOGGER.warn(String.format("Log dropped due to invalid timestamp, %s", event.encode()));
                return;
            }
        }

        var source = event.getString(EVENT_SOURCE);

        if (!sources.contains(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID)) && !categories.contains(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY)))
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(source + " registered with plugin id = " + event.getInteger(PLUGIN_ID) + " & category = " + event.getString(EVENT_CATEGORY));
            }

            sources.add(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID));

            categories.add(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));

            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY)).put(PLUGIN_ID, event.getInteger(PLUGIN_ID)));

            vertx.eventBus().publish(EVENT_EVENT_CATEGORY_UPDATE, event.getString(EVENT_SOURCE_TYPE) + SEPARATOR + event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));
        }
        else if (!sources.contains(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID)))
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(source + " registered with plugin id = " + event.getInteger(PLUGIN_ID));

            }

            sources.add(source + DASH_SEPARATOR + event.getInteger(PLUGIN_ID));

            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(PLUGIN_ID, event.getInteger(PLUGIN_ID)));
        }
        else if (!categories.contains(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY)))
        {
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(source + " registered with category = " + event.getString(EVENT_CATEGORY));
            }

            categories.add(source + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));

            vertx.eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(EVENT_SOURCE, source).put(EVENT, EVENT_LOG).put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE).put(EVENT_CATEGORY, event.getString(EVENT_CATEGORY)));

            vertx.eventBus().publish(EVENT_EVENT_CATEGORY_UPDATE, event.getString(EVENT_SOURCE_TYPE) + SEPARATOR + event.getInteger(PLUGIN_ID) + DASH_SEPARATOR + event.getString(EVENT_CATEGORY));
        }

        var values = stats.computeIfAbsent(event.getString(EVENT_SOURCE) + SEPARATOR + event.getString(EVENT_CATEGORY) + SEPARATOR + event.getString(EVENT_SOURCE_TYPE), k -> new long[2]);

        values[0]++; // count

        values[1] += event.getInteger(EVENT_VOLUME_BYTES, 0); // volume bytes

        if (CommonUtil.isNotNullOrEmpty(LogForwarderConfigStore.getStore().getItems()))
        {
            vertx.eventBus().send(EVENT_LOG_FORWARDER, event);
        }

        LogEngineConstants.write(event, mappers, builder, indexableColumns,LOGGER);

        policyEngine.processEvent(event);
    }

    /**
     * Sets the event timestamp based on the parser configuration.
     * <p>
     * This method determines and sets the appropriate timestamp for a log event based on:
     * <ul>
     *   <li>The parser type (custom, regex, JSON, delimiter)</li>
     *   <li>The date/time formatter type (formatter or numeric)</li>
     *   <li>The date/time unit (seconds or milliseconds)</li>
     * </ul>
     * <p>
     * The method handles several scenarios:
     * <ul>
     *   <li>For custom parsers: Uses the timestamp provided by the plugin</li>
     *   <li>For numeric timestamps: Converts from milliseconds to seconds if needed</li>
     *   <li>For formatted timestamps: Parses the timestamp using the configured formatter</li>
     *   <li>If no timestamp is found: Uses the received timestamp as a fallback</li>
     * </ul>
     *
     * @param context The parser configuration containing timestamp formatting information
     */
    private void setEventTime(JsonObject context)
    {
        try
        {
            enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.getValue(RECEIVED_TIMESTAMP));  // default = received

            if (CUSTOM.equals(valueOfName(CommonUtil.getString(context.getString(LOG_PARSER_TYPE)))) && enrichedEvent.containsKey(TIME_STAMP))
            {
                enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.remove(TIME_STAMP));   // it's always in seconds
            }

            else if (enrichedEvent.containsKey(TIME_STAMP))
            {
                if (LogDateTimeFormatterType.NUMERIC.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMATTER_TYPE)))
                {
                    if (LogDateTimeUnit.SECONDS.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMAT)))
                    {
                        enrichedEvent.put(EVENT_TIMESTAMP, enrichedEvent.remove(TIME_STAMP));
                    }
                    else if (LogDateTimeUnit.MILLIS.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMAT)))
                    {
                        enrichedEvent.put(EVENT_TIMESTAMP, CommonUtil.getLong(enrichedEvent.remove(TIME_STAMP)) / 1000);    // ms -> sec
                    }
                }

                else if (LogDateTimeFormatterType.FORMATTER.getName().equals(context.getString(LOG_PARSER_DATE_TIME_FORMATTER_TYPE)) && dateTimeFormatters.get(context.getLong(ID)) != null)
                {
                    enrichedEvent.put(EVENT_TIMESTAMP, DateTimeUtil.timestampToSeconds(dateTimeFormatters.get(context.getLong(ID)), CommonUtil.getString(enrichedEvent.remove(TIME_STAMP)), timezonesBySource.getOrDefault(enrichedEvent.getString(EVENT_SOURCE), "")));
                }

                enrichedEvent.remove(TIME_STAMP);
            }
        }

        catch (Exception exception)
        {
            LOGGER.warn(exception);
        }
    }

    private void parseLogByPattern(JsonObject processor, String log)
    {
        var pattern = patternsByProcessor.get(processor.getLong(ID));

        try
        {
            if (pattern != null)
            {
                var matcher = pattern.matcher(log);

                if (matcher.find())
                {
                    var a = matcher.namedGroups();

                    if (!a.isEmpty())
                    {
                        for(var group : a.entrySet())
                        {
                            enrichedEvent.put(group.getKey(),matcher.group(group.getKey()));
                        }
                    }
                    else
                    {
                        if (matcher.groupCount() > 0)
                        {
                            for (var index = 1; index <= matcher.groupCount(); index++)
                            {
                                if (matcher.group(index) != null)
                                {
                                    enrichedEvent.put("Field " + index, matcher.group(index).trim());
                                }
                            }
                        }

                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void loadIndexableColumns()
    {
        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + INDEXABLE_COLUMNS);

            if (file.exists())
            {
                var buffer = vertx.fileSystem().readFileBlocking(file.getPath());

                if (buffer != null && buffer.getBytes().length > 0)
                {
                    indexableColumns.mergeIn(new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))));
                }
            }
            else
            {
                vertx.fileSystem().createFileBlocking(file.getPath());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private boolean evaluateFilter(JsonObject filter, JsonObject event)
    {
        if (filter == null || filter.isEmpty())
        {
            return true;
        }

        var groupOperator = filter.getString(OPERATOR);

        var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

        for (var index = 0; index < conditionGroups.size(); index++)
        {
            var conditionGroup = conditionGroups.getJsonObject(index);

            var operator = conditionGroup.getString(OPERATOR);

            var conditions = conditionGroup.getJsonArray(CONDITIONS);

            var satisfied = false;

            for (var j = 0; j < conditions.size(); j++)
            {
                var condition = conditions.getJsonObject(j);

                var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ?
                        condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] :
                        condition.getString(OPERAND);

                if (event.containsKey(operand))
                {
                    satisfied = PolicyEngineConstants.evaluateCondition(
                            conditionGroup.getString(FILTER).equalsIgnoreCase("include"),
                            condition.getString(OPERATOR),
                            condition.getValue(VALUE),
                            event.getValue(operand));

                    if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) ||
                            (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                    {
                        break;
                    }
                }
            }

            if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
            {
                return false;
            }
            else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
            {
                return true;
            }
        }

        return true;
    }

    /**
     * Enriches a log event with additional field data.
     * <p>
     * This method adds a field value to the enriched event, handling special cases:
     * <ul>
     *   <li>Timestamp fields - Stored separately and removed from the message</li>
     *   <li>Metadata fields - Added without a prefix</li>
     *   <li>IP address fields - Resolved to geographic information when possible</li>
     *   <li>Regular fields - Added with the appropriate prefix</li>
     * </ul>
     * <p>
     * The method also handles field name normalization, replacing non-alphanumeric
     * characters to ensure valid field names.
     *
     * @param prefix    The prefix to add to the field name (typically the parser name)
     * @param value     The value to add to the enriched event
     * @param field     The field name
     * @param type      The field type
     * @param message   The original log message
     * @param testProbe Whether this is a test probe (affects field name handling)
     */
    private void enrich(String prefix, Object value, String field, String type, String message, boolean testProbe)
    {
        if (value != null)
        {
            if (TIME_STAMP.equalsIgnoreCase(type))  // timestamp type field should have named timestamp
            {
                enrichedEvent.put(TIME_STAMP, value);

                enrichedEvent.put(MESSAGE, message.replace(CommonUtil.getString(value), EMPTY_VALUE).trim());   // removing timestamp from raw message
            }

            else if (METADATA_FIELDS.contains(field)) // if field is system field don't add prefix
            {
                enrichedEvent.put(field, value);
            }

            else if (!EVENT.equalsIgnoreCase(field))
            {
                var token = CommonUtil.getString(value);

                if (CommonUtil.isNotNullOrEmpty(token) && !token.equalsIgnoreCase("null") && !token.equals("-") && !token.equals("\\") && !token.equals("/")) // ignore garbage field
                {
                    if (field.endsWith(".ip") && !garbageIPAddresses.mightContain(CommonUtil.getString(value))) // if inverseMapper contains IP (no record found in DB) -> Send default
                    {
                        var result = GeoDBUtil.resolveIP(CommonUtil.getString(value), field.replace(".ip", EMPTY_VALUE).trim());

                        if (result == null)      // if not found in DB -> put into inverseMapper and send default
                        {
                            garbageIPAddresses.put(CommonUtil.getString(value));
                        }

                        else
                        {
                            result.getMap().keySet().forEach(key -> enrichedEvent.put(FIELD_PATTERN.matcher((prefix + "." + key).trim().toLowerCase(Locale.ROOT)).replaceAll("."), result.getValue(key)));
                        }
                    }

                    if (!testProbe)
                    {
                        var key = (prefix + "." + field).trim().toLowerCase(Locale.ROOT);

                        enrichedFields.computeIfAbsent(key, val -> FIELD_PATTERN.matcher(key).replaceAll("."));  // for other fields add prefix

                        enrichedEvent.put(enrichedFields.get(key), value);
                    }
                    else
                    {
                        /*
                         * MOTADATA-3039 : In case of event from user action for sample log file - log analytics in custom regex log parser creation,
                         * if we append parser name in field name and replace empty space with "." than it will differ from field name shown in UI.
                         * */
                        enrichedEvent.put(field, value);
                    }
                }
            }
        }
    }

    private void loadCountriesCsv(String fileName) throws Exception {
        try (BufferedReader br = new BufferedReader(new FileReader(fileName))) {
            String line;
            boolean isHeader = true;
            while ((line = br.readLine()) != null) {
                if (isHeader) {
                    isHeader = false; // skip first line (header)
                    continue;
                }
                String[] parts = line.split(",");
                if (parts.length < 3) continue; // skip invalid lines
                String country = parts[2].trim().toLowerCase();
                double lat = Double.parseDouble(parts[0]);
                double lon = Double.parseDouble(parts[1]);
                countryCoords.put(country, new Coordinate(lat, lon));
            }
        }
    }

    public static int handleCountryTravelTime(String from , String to) {

        double avgSpeed = 60;

        Coordinate c1 = countryCoords.get(from);
        Coordinate c2 = countryCoords.get(to);

        if (c1 == null || c2 == null)
        {
            return 0;
        }

        double distance = haversine(c1.lat, c1.lon, c2.lat, c2.lon);
        double hours = distance / avgSpeed;
        int h = (int) hours;
        int m = (int) ((hours - h) * 60);

        return (h*60) + m ;

    }

    public static double haversine(double lat1, double lon1, double lat2, double lon2) {
        final double R = 6371.0; // Earth's radius in KM
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        lat1 = Math.toRadians(lat1);
        lat2 = Math.toRadians(lat2);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.sin(dLon / 2) * Math.sin(dLon / 2) * Math.cos(lat1) * Math.cos(lat2);
        double c = 2 * Math.asin(Math.sqrt(a));
        return R * c;
    }

    private static class Coordinate {
        double lat, lon;
        Coordinate(double lat, double lon) { this.lat = lat; this.lon = lon; }
    }
    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }

    record Plugin(Object instance, Method method)
    {

    }
}
