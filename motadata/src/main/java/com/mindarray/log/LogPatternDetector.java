/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.LogParser;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.xerial.snappy.Snappy;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.mindarray.GlobalConstants.SEVERITY;
import static com.mindarray.api.LogParser.LOG_PARSER_FIELDS;
import static java.util.regex.Pattern.CASE_INSENSITIVE;

/**
 * The LogPatternDetector class provides utilities for detecting and building patterns in log data.
 * <p>
 * This class is responsible for:
 * <ul>
 *   <li>Detecting timestamp patterns in log entries</li>
 *   <li>Building regex patterns for parsing log entries</li>
 *   <li>Identifying field positions within log entries</li>
 *   <li>Generating appropriate regex patterns for different types of log content</li>
 * </ul>
 * <p>
 * The class uses various pattern detection strategies to handle different log formats and structures.
 * It can detect timestamps in various formats and generate regex patterns that can be used for parsing logs.
 * <p>
 * This class is designed to be used as a utility class with static methods and should not be instantiated.
 *
 * @see LogParser
 * @see LogPatternBuilder
 */
public final class LogPatternDetector
{
    /**
     * Constant for the start position key in JSON objects
     */
    public static final String START_POS = "start.position";

    /**
     * Constant for the end position key in JSON objects
     */
    public static final String END_POS = "end.position";

    /**
     * Constant for the pattern key in JSON objects
     */
    public static final String PATTERN = "pattern";

    /**
     * Constant for the format key in JSON objects
     */
    public static final String FORMAT = "format";

    /**
     * Constant for the date formatter key in JSON objects
     */
    public static final String DATE_FORMATTER = "date.formatter";

    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(LogPatternDetector.class, GlobalConstants.MOTADATA_LOG, "Log Pattern Detector");

    /**
     * Pattern for matching word characters
     */
    private static final Pattern WORD_PATTERN = Pattern.compile("[\\w]");

    /**
     * Pattern for matching any character
     */
    private static final Pattern ANY_CHAR_PATTERN = Pattern.compile(".");

    /**
     * Pattern for matching one or more digits
     */
    private static final Pattern DIGIT_PATTERN = Pattern.compile("(\\d+)");

    /**
     * Map to store custom timestamp patterns with their formatters
     */
    private static final Map<String, PatternFormat<Pattern, DateTimeFormatter, String>> CUSTOM_PATTERNS = new LinkedHashMap<>();

    private static final Map<String, PatternFormat<Pattern, DateTimeFormatter, String>> TIMESTAMP_PATTERNS = new LinkedHashMap<>();

    private static final Map<String, List<List<PatternFormat<Pattern, List<DateTimeFormatter>, List<String>>>>> DATETIME_PATTERNS = new LinkedHashMap<>();

    private static final Pattern SE_PATTERN = Pattern.compile("\\b(alert|trace|debug|notice|info|warn(?:ing)?|error|critical|fatal|severe|emerg(?:ency)?)\\b",CASE_INSENSITIVE);

    /**
     * Private constructor to prevent instantiation of this utility class.
     * This class is designed to be used via its static methods only.
     */
    private LogPatternDetector()
    {
    }

    /**
     * Builds a regex pattern for parsing a log entry based on the provided log text and field positions.
     * <p>
     * This method analyzes the log text and the specified field positions to generate a regex pattern
     * that can be used to parse similar log entries. It automatically detects timestamp patterns and
     * generates appropriate regex patterns for different parts of the log entry.
     * <p>
     * The method returns a JsonObject containing:
     * <ul>
     *   <li>The generated regex pattern</li>
     *   <li>Information about detected fields</li>
     *   <li>Timestamp format if detected</li>
     *   <li>Field positions</li>
     * </ul>
     *
     * @param log       The log text to analyze
     * @param positions A JsonArray containing the start and end positions of fields to capture
     * @return A JsonObject containing the generated pattern and field information
     */
    public static JsonObject buildPattern(String log, JsonArray positions)
    {
        var result = new JsonObject();

        var builder = new StringBuilder();

        var positionIndex = 0;

        String detectedPattern;

        var specialCharacter = ' ';

        // Pattern to match any non-word, non-whitespace character (special characters)
        var specialCharPattern = Pattern.compile("[^\\w\\s]");

        var timestampEndPos = 0;

        String timestampRegex = null;

        var timestampStartPos = 0;

        Matcher matcher;

        // Array to store information about detected fields
        var fields = new JsonArray();

        // First, try to detect a timestamp pattern in the log
        var timestamp = detectTimePattern(log);

        if (!timestamp.isEmpty())
        {
            String timestampValue;

            // Get the regex pattern for the timestamp
            timestampRegex = timestamp.getString(PATTERN);

            // Get the actual timestamp value
            timestampValue = timestamp.getString(GlobalConstants.TIME_STAMP);

            // Store the timestamp format in the result
            result.put(LogParser.LOG_PARSER_DATE_TIME_FORMAT, timestamp.getString(FORMAT));

            if (timestampValue != null && !timestampValue.trim().isEmpty())
            {
                // Find the position of the timestamp in the log
                timestampStartPos = log.indexOf(timestampValue);

                timestampEndPos = timestampStartPos + timestampValue.length();
            }

            var startPos = timestampStartPos;

            // Add the timestamp position to the positions array if it's not already there
            if (positions.stream().noneMatch(item -> JsonObject.mapFrom(item).getInteger(START_POS) == startPos))
            {
                positions.add(new JsonObject().put(START_POS, timestampStartPos).put(END_POS, timestampEndPos));
            }
        }

        // Sort the positions to ensure they're processed in order
        sort(positions.getList());

        // Process each position to build the regex pattern
        while (positionIndex < positions.size())
        {
            // Check if this is the last position and extends to the end of the log
            // If so, we'll use a greedy pattern to match everything to the end
            var greedyPattern = positionIndex == positions.size() - 1 && positions.getJsonObject(positionIndex).getInteger(END_POS) == log.length();

            var startPosition = positions.getJsonObject(positionIndex).getInteger(START_POS);

            // Handle the special case where the end position is at the end of the log
            var endPosition = positions.getJsonObject(positionIndex).getInteger(END_POS) == log.length() ? log.length() - 1 : positions.getJsonObject(positionIndex).getInteger(END_POS);

            var endBoundary = log.charAt(endPosition);

            var capturedValue = "";

            // Extract the captured value from the log
            if (endPosition == log.length() - 1)
            {
                capturedValue = log.substring(startPosition, endPosition + 1);
            }
            else
            {
                capturedValue = log.substring(startPosition, endPosition);
            }

            // Generate a regex pattern for the captured value
            var pattern = detectPattern(capturedValue, endBoundary);

            // Calculate the distance between this position and the previous one
            // This helps determine how to generate the pattern for the text between positions
            var groupDistance = (positionIndex != 0) ? startPosition - positions.getJsonObject(positionIndex - 1).getInteger(END_POS) : Math.max(startPosition - 1, 0);

            // For small distances, generate a specific pattern for the characters
            if (groupDistance <= 3 && groupDistance != 0)
            {
                detectedPattern = detectPattern(log.substring((positionIndex != 0 ? positions.getJsonObject(positionIndex - 1).getInteger(END_POS) : 0), startPosition));
            }
            else
            {
                // For larger distances, try to find a special character to use as a pattern anchor
                // Start backtracking from just before the current position
                var backTrackerIndex = Math.max(startPosition - 1, 0);

                // Limit backtracking to 30 characters to avoid excessive processing
                var maxBackTrackingLimit = Math.min(backTrackerIndex, 30);

                var previousCapturedPosition = (positionIndex != 0) ? positions.getJsonObject(positionIndex - 1).getInteger(END_POS) : 0;

                // Backtrack until we find a special character or reach the previous position
                while (maxBackTrackingLimit != -1 && backTrackerIndex != previousCapturedPosition - 1)
                {
                    var currentCharacter = log.charAt(backTrackerIndex);

                    matcher = specialCharPattern.matcher(String.valueOf(currentCharacter));

                    if (matcher.find())
                    {
                        specialCharacter = matcher.group().charAt(0);
                        break;
                    }

                    backTrackerIndex--;
                    maxBackTrackingLimit--;
                }

                if (specialCharacter != ' ')
                {
                    // If we found a special character, generate a pattern that hops to it
                    detectedPattern = detectPattern(log, specialCharacter, previousCapturedPosition, backTrackerIndex);

                    // If there's still distance between the special character and the current position,
                    // generate a pattern for that remaining text
                    if (startPosition - backTrackerIndex > 0)
                    {
                        detectedPattern += detectPattern(log.substring(backTrackerIndex + 1, startPosition));
                    }
                }
                else
                {
                    // If no special character was found, use spaces as delimiters
                    detectedPattern = detectSpacePattern(log, previousCapturedPosition, startPosition);
                }
            }

            // If this position matches the timestamp position, use the timestamp regex
            if (timestampStartPos == startPosition && timestampEndPos == endPosition && timestampRegex != null && !timestampRegex.trim().isEmpty())
            {
                pattern = timestampRegex;
            }

            // For the last position that extends to the end, use a greedy pattern
            if (greedyPattern)
            {
                pattern = "(.*)";
            }

            // Append the detected pattern and the field pattern to the main regex
            builder.append(detectedPattern).append(pattern);

            // Reset the special character for the next iteration
            specialCharacter = ' ';

            positionIndex++;

            // Add information about this field to the fields array
            fields.add(new JsonObject()
                    .put(LogParser.LogParserField.TYPE.getName(),
                            (timestampStartPos == startPosition && timestampEndPos == endPosition) ? GlobalConstants.TIME_STAMP : "none")
                    .put(LogParser.LogParserField.VALUE.getName(), capturedValue)
                    .put(LogParser.LogParserField.NAME.getName(),
                            (timestampStartPos == startPosition && timestampEndPos == endPosition) ? GlobalConstants.TIME_STAMP : "Field " + positionIndex));
        }

        // If we successfully built a pattern and identified fields, add them to the result
        if (!builder.isEmpty() && !fields.isEmpty())
        {
            result.put(LOG_PARSER_FIELDS, fields);
            result.put(LogParser.LOG_PARSER_LOG_POSITIONS, positions);
            result.put(LogEngineConstants.LogParserType.REGEX.getName(), "^" + builder);
        }

        return result;
    }

    /**
     * Generates a regex pattern for capturing a specific value based on its end boundary character.
     * <p>
     * This method generates regex in two different ways:
     * <ol>
     *   <li>If the end boundary character is not present inside the captured value, it generates a pattern
     *       that matches everything up to that character.</li>
     *   <li>If the end boundary character is present inside the captured value or is a word character,
     *       it generates a specific regex pattern using the {@link #detectPattern(String)} method.</li>
     * </ol>
     * <p>
     * For digit-only values, it generates a specific digit pattern.
     *
     * @param capturedValue The value to generate a pattern for
     * @param endPosition   The character that marks the end boundary
     * @return A regex pattern enclosed in capturing parentheses
     */
    static String detectPattern(String capturedValue, char endPosition)
    {

        String pattern;

        //Second way
        if ((capturedValue.contains(String.valueOf(endPosition)) || WORD_PATTERN.matcher(String.valueOf(endPosition)).find()))
        {
            pattern = detectPattern(capturedValue);
        }
        else if (DIGIT_PATTERN.matcher(capturedValue).matches())
        {
            pattern = "\\d+";
        }
        // First way
        else
        {
            pattern = "[^\\" + endPosition + "]*";
        }

        return "(" + pattern + ")";
    }

    /**
     * Generates a regex pattern based on the number of spaces between two positions in a string.
     * <p>
     * This method counts the number of spaces in the substring between the specified start and end positions,
     * and generates a regex pattern that matches the same number of space-separated tokens.
     * <p>
     * The generated pattern is useful for matching log entries with consistent spacing between fields.
     *
     * @param value         The string to analyze
     * @param startPosition The starting position in the string
     * @param endPosition   The ending position in the string
     * @return A regex pattern that matches the same number of space-separated tokens
     */
    static String detectSpacePattern(String value, int startPosition, int endPosition)
    {
        var spaceChars = value.substring(startPosition, endPosition).chars().filter(ch -> ch == ' ').count();

        var pattern = "";

        if (spaceChars > 0)
        {
            pattern = "(?:[^\\ ]*\\ ){" + spaceChars + "}";
        }

        return pattern;
    }

    /**
     * Generates a "hopping" regex pattern based on occurrences of a special character.
     * <p>
     * This method counts the number of occurrences of the specified special character in the substring
     * between the start and end positions, and generates a regex pattern that matches the same number
     * of tokens separated by that special character.
     * <p>
     * The generated pattern is useful for matching log entries with consistent special character separators.
     * This is particularly useful when backtracking to find patterns in log entries.
     *
     * @param value            The string to analyze
     * @param specialCharacter The special character to count and use as a separator
     * @param startPosition    The starting position in the string
     * @param endPosition      The ending position in the string
     * @return A regex pattern that matches the same number of tokens separated by the special character
     */
    static String detectPattern(String value, char specialCharacter, int startPosition, int endPosition)
    {

        var specialChars = value.substring(startPosition, endPosition + 1).chars().filter(ch -> ch == specialCharacter).count();

        var pattern = "";

        if (specialChars > 0)
        {
            pattern = "(?:[^\\" + specialCharacter + "]*\\" + specialCharacter + "){" + specialChars + "}";
        }

        return pattern;
    }

    /**
     * Generates a specific regex pattern for a given string by analyzing its character types.
     * <p>
     * This method analyzes each character in the input string and generates a regex pattern
     * that matches strings with similar character patterns. It recognizes:
     * <ul>
     *   <li>Digits - represented as \d or \d+</li>
     *   <li>Word characters - represented as \w or \w+</li>
     *   <li>Special characters - represented literally with escape sequences if needed</li>
     * </ul>
     * <p>
     * For example, the string "hello world9" would generate a pattern like "\w+\s\w+\d".
     * <p>
     * This method is useful for generating patterns that match specific text structures
     * without being tied to the exact content.
     *
     * @param value The string to analyze
     * @return A regex pattern that matches strings with similar character patterns
     */
    public static String detectPattern(String value)
    {
        // Create a matcher that will match each character in the input string
        var anyCharMatcher = ANY_CHAR_PATTERN.matcher(value);

        String token;

        var builder = new StringBuilder();

        // Initialize the tokens list with an empty string
        var tokens = new ArrayList<>(List.of(""));

        // Process each character in the input string
        while (anyCharMatcher.find())
        {
            var anyCharacter = anyCharMatcher.group();

            // Handle digit characters
            if (anyCharacter.matches("[\\d]"))
            {
                token = "\\d";

                // If the previous token was also a digit, combine them into a \d+ pattern
                if (Objects.equals(tokens.getLast(), "\\d") || Objects.equals(tokens.getLast(), "\\d+"))
                {
                    tokens.removeLast();
                    token = "\\d+";
                }
            }
            // Handle word characters (letters, digits, underscores)
            else if (anyCharacter.matches("[\\w]"))
            {
                token = "\\w";

                // If the previous token was also a word character, combine them into a \w+ pattern
                if (Objects.equals(tokens.getLast(), "\\w") || Objects.equals(tokens.getLast(), "\\w+"))
                {
                    tokens.removeLast();
                    token = "\\w+";
                }
            }
            // Handle special characters (non-word, non-whitespace)
            else
            {
                // Escape the special character in the regex
                token = "\\" + anyCharMatcher.group();
            }

            // Add the token to our list
            tokens.add(token);
        }

        // Build the final regex pattern from the tokens
        var tokenIndex = 0;
        while (tokens.size() > tokenIndex)
        {
            builder.append(tokens.get(tokenIndex));
            tokenIndex++;
        }

        return builder.toString();
    }

    /**
     * Sorts a list of text positions based on their start positions.
     * <p>
     * This method sorts the provided list of text positions in ascending order based on their
     * start position values. This ensures that the positions are processed in the correct order
     * when building patterns.
     * <p>
     * Each text position in the list is expected to be convertible to a JsonObject containing
     * a "start.position" field.
     *
     * @param textPositions A list of objects representing text positions selected by the user
     */
    public static void sort(List<Object> textPositions)
    {
        textPositions.sort(Comparator.comparing(textPosition -> JsonObject.mapFrom(textPosition).getInteger(START_POS)));
    }

    /**
     * Detects timestamp patterns in the provided text.
     * <p>
     * This method attempts to identify timestamp patterns in the input text by delegating to
     * the {@link #detectDateTimePattern(String)} method. If a timestamp pattern is detected,
     * it returns a JsonObject containing:
     * <ul>
     *   <li>The detected timestamp value</li>
     *   <li>The format of the timestamp</li>
     *   <li>The regex pattern for matching similar timestamps</li>
     *   <li>The date formatter for parsing the timestamp</li>
     * </ul>
     * <p>
     * If no timestamp pattern is detected or an error occurs, an empty JsonObject is returned.
     *
     * @param text The text to analyze for timestamp patterns
     * @return A JsonObject containing timestamp information, or an empty JsonObject if none is found
     */
    public static JsonObject detectTimePattern(String text)
    {
        var result = new JsonObject();

        var matcher1 = SE_PATTERN.matcher(text);

        var severity = "info";

        if(matcher1.find())
        {
            severity = matcher1.group(1);
        }

        result.put(SEVERITY,severity);

        // Check Special Regex first
        for (var item : CUSTOM_PATTERNS.entrySet())
        {
            var matcher = item.getValue().regex().matcher(text);

            if (matcher.find())
            {
                try
                {
                    // Check if valid format
                    item.getValue().dateFormatter().parseDateTime(matcher.group(0).trim());

                    result.put(PATTERN, item.getValue().regex().pattern());

                    result.put(FORMAT, item.getValue().format());

                    result.put(GlobalConstants.TIME_STAMP, matcher.group(0).trim());

                    result.put(DATE_FORMATTER, item.getValue().dateFormatter());

                    return result;
                }
                catch (UnsupportedOperationException | IllegalArgumentException exception)
                {
                    LOGGER.error(exception);
                }
            }
        }

        // Check for timestamp
        for (var item : TIMESTAMP_PATTERNS.entrySet())
        {
            var matcher = item.getValue().regex().matcher(text);

            if (matcher.find())
            {
                try
                {
                    // Check if valid format
                    item.getValue().dateFormatter().parseDateTime(matcher.group(0).trim());

                    //put it into result
                    result.put(PATTERN, item.getValue().regex().pattern());

                    result.put(FORMAT, item.getValue().format());

                    result.put(GlobalConstants.TIME_STAMP, matcher.group(0).trim());

                    result.put(DATE_FORMATTER, item.getValue().dateFormatter());

                    break;
                }
                catch (UnsupportedOperationException | IllegalArgumentException exception)
                {
                    LOGGER.error(exception);
                }
            }
        }

        // Find Date irrespective of we found timestamp or not.
        for (var entry : DATETIME_PATTERNS.get(result.getString(FORMAT, "")))
        {
            var dateTimestamp = detectDatePattern(entry, text);

            if (!dateTimestamp.isEmpty())
            {
                // todo: support multiple formats

                var first = dateTimestamp.getJsonObject(0);

                result.put(GlobalConstants.TIME_STAMP, first.getString(GlobalConstants.TIME_STAMP));

                result.put(FORMAT, first.getString(FORMAT));

                result.put(PATTERN, first.getString(PATTERN));

                result.put(DATE_FORMATTER, first.getValue(DATE_FORMATTER));

                break;
            }
        }

        // Find severity



        return result;
    }

    private static JsonArray detectDatePattern(List<PatternFormat<Pattern, List<DateTimeFormatter>, List<String>>> patternFormats, String message)
    {
        var result = new JsonArray();

        for (var patternFormat : patternFormats)
        {
            var matcher = patternFormat.regex().matcher(message);

            if (matcher.find())
            {
                var dateFormatter = patternFormat.dateFormatter();

                for (var index = 0; index < dateFormatter.size(); index++)
                {
                    var formatter = dateFormatter.get(index);

                    try
                    {
                        formatter.parseDateTime(matcher.group(0).trim());

                        result.add(new JsonObject()
                                .put(GlobalConstants.TIME_STAMP, matcher.group(0).trim())
                                .put(PATTERN, patternFormat.regex().pattern())
                                .put(FORMAT, patternFormat.format().get(index))
                                .put(DATE_FORMATTER, patternFormat.dateFormatter().get(index)));

                    }
                    catch (Exception parseException)
                    {
                        LOGGER.error(parseException);
                    }
                }
            }
        }

        return result;
    }

    /**
     * Loads custom timestamp patterns from a configuration file.
     * <p>
     * This method reads timestamp patterns from a "custom-timestamps.json" file in the current directory
     * and loads them into the CUSTOM_PATTERNS map. Each pattern consists of:
     * <ul>
     *   <li>A regex pattern for matching timestamps</li>
     *   <li>A date format string for parsing the timestamp</li>
     * </ul>
     * <p>
     * The method executes asynchronously using Vert.x's executeBlocking method to avoid blocking the event loop.
     * If the file doesn't exist or an error occurs during loading, the method logs the error and continues.
     */
    public static void loadDefaultFormats()
    {
        Bootstrap.vertx().executeBlocking(future -> {

            try
            {
                // load custom-regexes
                // Custom regexes is a set of regexes that has special nature and not being parsed by or timestamps dataset.
                // anyone can add custom regexes with date format to parse timestamp
                var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "custom-timestamps.json");

                if (file.exists())
                {
                    var customTimestamps = new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8));

                    for (var item : customTimestamps)
                    {
                        CUSTOM_PATTERNS.put(item.getValue().toString(), new PatternFormat<>(Pattern.compile(item.getKey()), DateTimeFormat.forPattern(item.getValue().toString()), item.getValue().toString()));
                    }
                }

                // load time and date regexes from timestamp-regexes
                file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.DB_DIR + GlobalConstants.PATH_SEPARATOR + "timestamp.db");

                if (file.exists())
                {
                    var timestampContainer = new JsonObject(new CipherUtil().decrypt(Snappy.uncompressString(Files.readAllBytes(file.toPath()))));

                    // load time regexes
                    for (var item : timestampContainer.getJsonObject("time.regexes"))
                    {
                        TIMESTAMP_PATTERNS.put(item.getValue().toString(), new PatternFormat<>(Pattern.compile(item.getKey()), DateTimeFormat.forPattern(item.getValue().toString()), item.getValue().toString()));
                    }

                    // load datetime regexes
                    var datetimeRegexes = timestampContainer.getJsonObject("datetime.regexes");

                    for (var timeGroup : datetimeRegexes)
                    {
                        var batches = datetimeRegexes.getJsonObject(timeGroup.getKey());

                        DATETIME_PATTERNS.put(timeGroup.getKey(), new ArrayList<>());

                        for (var batch : batches)
                        {
                            var regexes = batches.getJsonObject(batch.getKey());

                            var list = new ArrayList<PatternFormat<Pattern, List<DateTimeFormatter>, List<String>>>();

                            for (var regex : regexes)
                            {
                                String key = regex.getKey();

                                var dateTimeFormatters = new ArrayList<DateTimeFormatter>();

                                var formats = new ArrayList<String>();

                                for (var format : regexes.getJsonArray(key))
                                {
                                    try
                                    {
                                        dateTimeFormatters.add(DateTimeFormat.forPattern(format.toString()));

                                        formats.add(format.toString());
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);
                                    }
                                }

                                list.add(new PatternFormat<>(Pattern.compile(key), dateTimeFormatters, formats));
                            }

                            DATETIME_PATTERNS.get(timeGroup.getKey()).add(list);
                        }
                    }
                }

                future.complete();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception);
            }

        }, handler -> {
        });
    }

    public static JsonObject parseLogByPattern(String log, Pattern pattern)
    {
        var result = new JsonObject();

        try
        {
            if (pattern != null)
            {
                var matcher = pattern.matcher(log);

                if (matcher.find())
                {
                    var a = matcher.namedGroups();

                    if (!a.isEmpty())
                    {
                        for(var group : a.entrySet())
                        {
                            result.put(group.getKey(),matcher.group(group.getKey()));
                        }
                    }
                    else
                    {
                        if (matcher.groupCount() > 0)
                        {
                            for (var index = 1; index <= matcher.groupCount(); index++)
                            {
                                if (matcher.group(index) != null)
                                {
                                    result.put(matcher.group(index), "Field " + index);
                                }
                            }
                        }

                    }

                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }
    /**
     * Detects date-time patterns in the provided string using custom patterns.
     * <p>
     * This method attempts to match the input string against the custom timestamp patterns
     * loaded in the CUSTOM_PATTERNS map. If a match is found and the matched text can be
     * successfully parsed as a date-time, it returns a JsonObject containing:
     * <ul>
     *   <li>The regex pattern that matched</li>
     *   <li>The format of the timestamp</li>
     *   <li>The matched timestamp value</li>
     *   <li>The date formatter for parsing the timestamp</li>
     * </ul>
     * <p>
     * If no match is found or the matched text cannot be parsed as a date-time,
     * an empty JsonObject is returned.
     *
     * @param value The string to analyze for date-time patterns
     * @return A JsonObject containing date-time information, or an empty JsonObject if none is found
     */
    private static JsonObject detectDateTimePattern(String value)
    {
        var result = new JsonObject();

        // Iterate through all custom timestamp patterns to find a match
        for (var item : CUSTOM_PATTERNS.entrySet())
        {
            // Try to match the input string against this pattern
            var matcher = item.getValue().regex().matcher(value);

            if (matcher.find())
            {
                try
                {
                    // Attempt to parse the matched text as a date-time using the pattern's formatter
                    // This validates that the matched text is actually a valid timestamp
                    item.getValue().dateFormatter().parseDateTime(matcher.group(0).trim());

                    // Store the regex pattern that matched
                    result.put(PATTERN, item.getValue().regex().pattern());

                    // Store the format string for the timestamp
                    result.put(FORMAT, item.getValue().format());

                    // Store the actual timestamp value that was matched
                    result.put(GlobalConstants.TIME_STAMP, matcher.group(0).trim());

                    // Store the date formatter for parsing the timestamp
                    result.put(DATE_FORMATTER, item.getValue().dateFormatter());

                    // Return the result immediately when a valid match is found
                    return result;
                }
                catch (UnsupportedOperationException | IllegalArgumentException exception)
                {
                    // Log any parsing errors but continue checking other patterns
                    LOGGER.error(exception);
                }
            }
        }

        // Return an empty result if no valid timestamp pattern was found
        return result;
    }

    /**
     * A record class that holds pattern information for timestamp detection.
     * <p>
     * This record stores:
     * <ul>
     *   <li>A regex pattern for matching timestamps</li>
     *   <li>A date formatter for parsing the timestamp</li>
     *   <li>A format string representing the timestamp format</li>
     * </ul>
     *
     * @param <R> The type of the regex pattern (typically {@link Pattern})
     * @param <D> The type of the date formatter (typically {@link DateTimeFormatter})
     * @param <F> The type of the format string (typically {@link String})
     */
    record PatternFormat<R, D, F>(R regex, D dateFormatter, F format)
    {
    }
}
