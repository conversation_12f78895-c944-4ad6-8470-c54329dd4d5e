/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 * 	Date			Author			     Notes
 *  03-July-2025     Smit Prajapati     MOTADATA-6540Verified consolidations verticles major modules.
 */

package com.mindarray.policy;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.store.EventSourceConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.*;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.EventPolicy.POLICY_RESULT_BY;
import static com.mindarray.api.EventPolicy.POLICY_SCHEDULED;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.log.LogPipeline.handleCountryTravelTime;
import static com.mindarray.policy.PolicyEngineConstants.*;

/**
 * Shared policy engine for event qualification and aggregation.
 * This class contains all the common policy processing logic that was duplicated
 * between LogParser and FlowProcessor.
 */
public class PolicyEngine {
    
    private static final Logger LOGGER = new Logger(PolicyEngine.class, MOTADATA_POLICY, "Policy Engine");
    
    /**
     * Maximum number of top groups to process in group-based policies
     */
    private static final int TOP_N_GROUPS = MotadataConfigUtil.getEventPolicyTopNGroups();
    
    /**
     * Maximum number of unique groups allowed in a policy
     */
    private static final int MAX_GROUPS = MotadataConfigUtil.getEventPolicyMaxGroups();
    
    // Policy management data structures
    private final Map<Long, JsonObject> policies = new HashMap<>();
    private final Map<String, JsonArray> newValuePolicy = new HashMap<>();
    private final Map<Long, Set<String>> newValues = new HashMap<>();
    private final Map<String, Long> distancePolicy = new HashMap<>();
    private final Map<Long, JsonArray> filters = new HashMap<>();
    private final JsonObject eventColumns = new JsonObject();
    private final Map<String, Map<String, List<Long>>> policiesBySource = new HashMap<>();
    private final Map<String, Map<String, List<Long>>> policiesBySourceType = new HashMap<>();
    private final Map<Long, Map<String, List<Long>>> policiesByGroup = new HashMap<>();
    private final Set<Long> suppressedPolicies = new HashSet<>();
    private final Set<String> numericColumns = new HashSet<>();
    private final Map<Long, Map<String, GroupStat>> policyGroupStats = new HashMap<>(64);
    private final Map<Long, String[]> groupingCounters = new HashMap<>();
    private final Map<Long,Map<String,JsonArray>> impossible = new HashMap<>(); // policy id -->( user  --> [contry, timestamp])
    private final String eventType;

    public PolicyEngine(String eventType) {
        this.eventType = eventType;
    }
    
    /**
     * Initialize the policy engine with event columns and policies
     */
    public void initialize(JsonObject eventColumns, JsonArray policies)
    {
        this.eventColumns.mergeIn(eventColumns);

        loadNumericColumns();
        
        for (var i = 0; i < policies.size(); i++)
        {
            var policy = policies.getJsonObject(i);

            if (isApplicablePolicy(policy))
            {
                addPolicy(policy);
            }
        }

        qualifyPolicies();
    }
    
    /**
     * Check if policy applies to this event type
     */
    public boolean isApplicablePolicy(JsonObject policy)
    {
        return eventType.equalsIgnoreCase(policy.getString(POLICY_TYPE)) &&
               (!policy.containsKey(POLICY_ARCHIVED) || policy.getString(POLICY_ARCHIVED).equalsIgnoreCase(NO)) &&
               (!policy.containsKey(POLICY_SCHEDULED) || policy.getString(POLICY_SCHEDULED).equalsIgnoreCase(NO));
    }
    
    /**
     * Add or update a policy
     */
    public void addPolicy(JsonObject policy)
    {
        if (!isApplicablePolicy(policy)) return;
        
        policies.put(policy.getLong(ID), enrich(policy));

        var context = policy.getJsonObject(POLICY_CONTEXT);
        
        if (context.containsKey(POLICY_RESULT_BY))
        {
            filters.put(policy.getLong(ID), new JsonArray()
                .addAll(context.getJsonArray(POLICY_RESULT_BY))
                .add(context.getString(VisualizationConstants.DATA_POINT)));

            policyGroupStats.put(policy.getLong(ID), new HashMap<>());

            groupingCounters.remove(policy.getLong(ID));
        }
        else
        {
            filters.put(policy.getLong(ID), new JsonArray()
                .add(context.getString(VisualizationConstants.DATA_POINT)));
        }

        if("new-value".equalsIgnoreCase(context.getString("log.policy.type")))
        {
            newValuePolicy.put(context.getString("counter"), new JsonArray().add(policy.getLong(ID)).add(context.getString("value")));
        }

        if("impossible-travel".equalsIgnoreCase(context.getString("log.policy.type")))
        {
            distancePolicy.put(context.getString("counter"), policy.getLong(ID));
        }

        qualifyPolicies();
    }

    /**
     * Remove a policy
     */
    public void removePolicy(Long policyId)
    {
        policies.remove(policyId);

        filters.remove(policyId);

        policyGroupStats.remove(policyId);

        groupingCounters.remove(policyId);

        qualifyPolicies();
    }
    
    /**
     * Suppress/unsuppress a policy
     */
    public void suppressPolicy(Long policyId, boolean suppress)
    {
        if (suppress)
        {
            suppressedPolicies.add(policyId);
        }
        else
        {
            suppressedPolicies.remove(policyId);
        }
    }
    
    /**
     * Check if policy is suppressed
     */
    public boolean isPolicySuppressed(Long policyId)
    {
        return suppressedPolicies.contains(policyId);
    }
    
    /**
     * Update event columns
     */
    public void updateEventColumns(String[] tokens)
    {
        update(eventColumns, tokens,false);

        if (CommonUtil.getInteger(tokens[0]) == CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName()))
        {
            numericColumns.add(tokens[2].trim());
        }

        qualifyPolicies();
    }
    
    /**
     * Get policy statistics for a given policy ID
     */
    public JsonObject getPolicyStats(Long policyId)
    {
        if (!policies.containsKey(policyId))
        {
            return new JsonObject();
        }
        
        var stats = policyGroupStats.get(policyId);

        if(stats != null && !stats.isEmpty())
        {
            var values = new ArrayList<>(stats.values());

            var aggregator = policies.get(policyId).getJsonObject(POLICY_CONTEXT)
                    .getString(VisualizationConstants.AGGREGATOR);

            GroupStat.sort(values, aggregator, TOP_N_GROUPS);

            stats.clear();

            return transform(values);
        }

        return new JsonObject();
    }
    
    /**
     * Qualify an event against applicable policies and aggregate if needed
     */
    public void processEvent(JsonObject event)
    {
        qualify(event);
    }
    
    private void loadNumericColumns()
    {
        for (var entry : eventColumns.getMap().entrySet())
        {
            var item = (JsonObject) entry.getValue();

            if (item.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES)
                .contains(CommonUtil.getInteger(DatastoreConstants.DataCategory.NUMERIC.getName())))
            {
                numericColumns.add(entry.getKey());
            }
        }
    }
    
    private JsonObject enrich(JsonObject policy)
    {
        try
        {
            var context = policy.getJsonObject(POLICY_CONTEXT);

            if (Operator.RANGE.getName().equalsIgnoreCase(context.getString(OPERATOR)))
            {
                var values = context.getString(VALUE).split(HASH_SEPARATOR);

                context.put(VALUE, new JsonArray().add(values[0]).add(values[1]));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return policy;
    }
    
    private JsonObject transform(Collection<GroupStat> stats)
    {
        var result = new JsonObject();

        for (var stat : stats)
        {
            result.put(stat.group, new JsonArray()
                .add(stat.count)
                .add(stat.sum)
                .add(stat.group));
        }

        return result;
    }

    public static void update(JsonObject columns, String[] tokens, boolean metric)
    {
        var column = tokens[DatastoreConstants.MetricWriterOrdinal.COLUMN.ordinal()];

        if (!columns.containsKey(column))
        {
            columns.put(column, new JsonObject());
        }

        var mapper = columns.getJsonObject(column);

        var plugins = mapper.getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS);

        if (plugins == null)
        {
            plugins = new JsonArray(new ArrayList<>(1));
        }

        if (!plugins.contains(CommonUtil.getInteger(tokens[1])))
        {
            mapper.put(DatastoreConstants.MAPPER_PLUGIN_IDS, plugins.add(CommonUtil.getInteger(tokens[1])));
        }

        var categories = mapper.getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

        if (categories == null)
        {
            categories = new JsonArray(new ArrayList<>(1));
        }

        if (!categories.contains(CommonUtil.getInteger(tokens[0])))
        {
            mapper.put(DatastoreConstants.MAPPER_DATA_CATEGORIES, categories.add(CommonUtil.getInteger(tokens[0])));
        }

        if (plugins.contains(DatastoreConstants.PluginId.CORRELATED_METRIC.getName()))
        {
            mapper.put(DatastoreConstants.MAPPER_CORRELATED, tokens[3]);
        }
        else
        {
            mapper.put(DatastoreConstants.MAPPER_STATUS, tokens[3]);
        }

        if (!metric && tokens.length > 4)
        {
            mapper.put(DatastoreConstants.MAPPER_EVENT_CATEGORY, tokens[4]);
        }

        if (column.contains(GlobalConstants.INSTANCE_SEPARATOR))
        {
            mapper.put(DatastoreConstants.MAPPER_INSTANCE, column.split(GlobalConstants.INSTANCE_SEPARATOR)[0]);
        }

        if (mapper.containsKey(DatastoreConstants.MAPPER_CORRELATED) && mapper.getString(DatastoreConstants.MAPPER_CORRELATED).equalsIgnoreCase(GlobalConstants.YES))
        {
            mapper.put(DatastoreConstants.MAPPER_GROUP, tokens[DatastoreConstants.EventWriterOrdinal.GROUP.ordinal()]);
        }
    }

    /**
     * Qualifies an event against applicable policies
     */
    private void qualify(JsonObject event)
    {
        try
        {
            var item = EventSourceConfigStore.getStore().getItemByHost(event.getString(EVENT_SOURCE), false);

            if (item != null)
            {
                for (var entry : assign(item.getJsonArray(LogEngineConstants.SOURCE_GROUPS), event.getString(EVENT_SOURCE), event.getString(LogEngineConstants.EVENT_SOURCE_TYPE)).entrySet())
                {
                    for (var value : entry.getValue())
                    {
                        var policy = policies.get(value);

                        if (policy.getString(POLICY_STATE).equalsIgnoreCase(YES) && !suppressedPolicies.contains(value))
                        {
                            var context = policy.getJsonObject(POLICY_CONTEXT);

                            if (event.containsKey(entry.getKey()) && event.getValue(entry.getKey()) != null)
                            {
                                var valid = !numericColumns.contains(entry.getKey()) || CommonUtil.getLong(event.getValue(entry.getKey())) > 0;

                                if (valid && context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                                {
                                    for (var i = 0; i < context.getJsonArray(POLICY_RESULT_BY).size(); i++)
                                    {
                                        var counter = context.getJsonArray(POLICY_RESULT_BY).getString(i);

                                        if (!event.containsKey(counter) && !CommonUtil.isNotNullOrEmpty(CommonUtil.getString(event.getValue(counter))))
                                        {
                                            valid = false;
                                        }
                                    }
                                }

                                if (valid)
                                {
                                    var filter = context.getJsonObject(FILTERS).getJsonObject(DATA_FILTER, null);

                                    var groupConditionSatisfied = false;

                                    if(EVENT_TRAP.equalsIgnoreCase(eventType))
                                    {
                                        groupConditionSatisfied = filterTrap(filter,event);
                                    }
                                    else
                                    {
                                        groupConditionSatisfied = evaluateFilter(filter, event);
                                    }

                                    if (groupConditionSatisfied)
                                    {
                                        if("new-value".equalsIgnoreCase(context.getString("log.policy.type")) && event.containsKey(context.getString("data.point")))
                                        {
                                            var values = newValues.get(policy.getLong(POLICY_ID));

                                            if(values.isEmpty())
                                            {
                                                //trigger
                                            }
                                            else
                                            {
                                                if(!values.contains(event.getString(context.getString("data.point"))))
                                                {
                                                    //trigger

                                                    values.add(event.getString(context.getString("data.point")));
                                                }
                                            }
                                        }
                                        else if ("impossible-travel".equalsIgnoreCase(context.getString("log.policy.type"))
                                         && event.containsKey(context.getString("data.point"))  && event.containsKey(context.getString("locationAttribute")))
                                        {

                                            var user = event.getString(context.getString("data.point"));
                                            var contry = event.getString(context.getString("locationAttribute"));

                                            if(impossible.containsKey(policy.getLong(ID)))
                                            {
                                                var item1 = impossible.get(policy.getLong(ID));

                                                if(item1.containsKey(user))
                                                {
                                                    var data = item1.get(user);

                                                    var min = handleCountryTravelTime(data.getString(0), contry);

                                                    if(event.getLong(EVENT_TIMESTAMP) - data.getLong(1) < (min*3600))
                                                    {
                                                        //trigger
                                                    }
                                                }
                                                else
                                                {
                                                    item1.put(user,new JsonArray().add(contry).add(event.getLong(EVENT_TIMESTAMP)));
                                                }

                                            }
                                            else
                                            {
                                                impossible.put(policy.getLong(ID), Map.ofEntries(Map.entry(event.getString(context.getString("data.point")),new JsonArray().add(event.getString(context.getString("locationAttribute"))).add(event.getLong(EVENT_TIMESTAMP)))));
                                            }
                                        }
                                        else
                                        {
                                            if(EVENT_TRAP.equalsIgnoreCase(eventType))
                                            {
                                                Bootstrap.vertx().eventBus().send(EVENT_EVENT_POLICY_INSPECT,event.put(POLICY_ID, value).put(EVENT,eventType));
                                            }
                                            else
                                            {
                                                var result = new JsonObject();

                                                for (var key : filters.get(policy.getLong(ID)))
                                                {
                                                    result.put(CommonUtil.getString(key), event.getString(CommonUtil.getString(key)));
                                                }

                                                aggregate(result.put(POLICY_ID, value).put(EVENT_TIMESTAMP, event.getLong(EVENT_TIMESTAMP)).put(EVENT_COPY_REQUIRED, false));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Aggregates an event against in-memory policies
     */
    private void aggregate(JsonObject event)
    {
        try
        {
            var policy = policies.get(event.getLong(POLICY_ID));

            if (policy != null)
            {
                var context = policy.getJsonObject(POLICY_CONTEXT);

                if (!event.containsKey(EVENT_TIMESTAMP))
                {
                    event.put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds());
                }

                if (context.containsKey(POLICY_RESULT_BY) && !context.getJsonArray(POLICY_RESULT_BY).isEmpty())
                {
                    var stats = policyGroupStats.get(policy.getLong(ID));

                    if (stats.size() > MAX_GROUPS)
                    {
                        LOGGER.warn(String.format("policy: %s, Unique groups are more than %d", policy.getString(POLICY_NAME), MAX_GROUPS));

                        var values = new ArrayList<>(stats.values());

                        var aggregator = context.getString(VisualizationConstants.AGGREGATOR);

                        GroupStat.sort(values, aggregator, TOP_N_GROUPS);

                        stats.clear();

                        for (var value : values)
                        {
                            stats.put(value.group, value);
                        }
                    }
                    else
                    {
                        var groupByFields = context.getJsonArray(POLICY_RESULT_BY);

                        var addGroupingCounters = false;

                        if (!groupingCounters.containsKey(policy.getLong(ID)))
                        {
                            groupingCounters.put(policy.getLong(ID), new String[4]);

                            addGroupingCounters = true;
                        }

                        var joiner = new StringJoiner(COLUMN_SEPARATOR);

                        for (var i = 0; i < groupByFields.size(); i++)
                        {
                            joiner.add(String.valueOf(event.getValue(groupByFields.getString(i))));

                            if (addGroupingCounters)
                            {
                                groupingCounters.get(policy.getLong(ID))[i] = groupByFields.getString(i);
                            }
                        }

                        var groupingKey = joiner.toString();

                        var stat = stats.computeIfAbsent(groupingKey, GroupStat::new);

                        stat.count++;

                        if (context.getString(VisualizationConstants.AGGREGATOR)
                                .equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()) ||
                                context.getString(VisualizationConstants.AGGREGATOR)
                                        .equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                        {
                            stat.sum += CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));
                        }
                    }
                }
                else
                {
                    // simple policy (no group by)
                    var stats = policyGroupStats.computeIfAbsent(policy.getLong(ID), k -> new HashMap<>());

                    var stat = stats.computeIfAbsent("default", GroupStat::new);

                    stat.count++;

                    if (context.getString(VisualizationConstants.AGGREGATOR)
                            .equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()) ||
                            context.getString(VisualizationConstants.AGGREGATOR)
                                    .equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
                    {
                        stat.sum += CommonUtil.getLong(event.getValue(context.getString(VisualizationConstants.DATA_POINT)));
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Evaluate filter conditions for an event
     */
    private boolean evaluateFilter(JsonObject filter, JsonObject event)
    {
        if (filter == null || filter.isEmpty())
        {
            return true;
        }

        var groupOperator = filter.getString(OPERATOR);

        var conditionGroups = filter.getJsonArray(CONDITION_GROUPS);

        for (var index = 0; index < conditionGroups.size(); index++)
        {
            var conditionGroup = conditionGroups.getJsonObject(index);

            var operator = conditionGroup.getString(OPERATOR);

            var conditions = conditionGroup.getJsonArray(CONDITIONS);

            var satisfied = false;

            for (var j = 0; j < conditions.size(); j++)
            {
                var condition = conditions.getJsonObject(j);

                var operand = condition.getString(OPERAND).contains(CARET_SEPARATOR) ?
                    condition.getString(OPERAND).split(CARET_SEPARATOR_WITH_ESCAPE)[0] :
                    condition.getString(OPERAND);

                if (event.containsKey(operand))
                {
                    satisfied = PolicyEngineConstants.evaluateCondition(
                        conditionGroup.getString(FILTER).equalsIgnoreCase("include"),
                        condition.getString(OPERATOR),
                        condition.getValue(VALUE),
                        event.getValue(operand));

                    if ((satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName())) ||
                        (!satisfied && operator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName())))
                    {
                        break;
                    }
                }
            }

            if (!satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.AND.getName()))
            {
                return false;
            }
            else if (satisfied && groupOperator.equalsIgnoreCase(DatastoreConstants.ConditionGroup.OR.getName()))
            {
                return true;
            }
        }

        return true;
    }

    /**
     * Maps policies to event sources, source types, and groups
     */
    private void qualifyPolicies()
    {
        policiesBySource.clear();

        policiesByGroup.clear();

        policiesBySourceType.clear();

        try
        {
            policies.forEach((key, value) -> {

                var policyContext = value.getJsonObject(POLICY_CONTEXT);

                var dataPoint = policyContext.getString(VisualizationConstants.DATA_POINT);

                JsonArray entities;

                if (policyContext.getJsonArray(ENTITIES) != null && !policyContext.getJsonArray(ENTITIES).isEmpty())
                {
                    entities = policyContext.getJsonArray(ENTITIES);

                    if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()))
                    {
                        assignById(value.getLong(ID), dataPoint, entities, policiesByGroup);
                    }
                    else if (policyContext.getString(ENTITY_TYPE).equalsIgnoreCase(VisualizationConstants.VisualizationGrouping.EVENT_SOURCE_TYPE.getName()))
                    {
                        assignBySource(value.getLong(ID), dataPoint, entities, policiesBySourceType);
                    }
                    else
                    {
                        assignBySource(value.getLong(ID), dataPoint, entities, policiesBySource);
                    }
                }
                else
                {
                    if (dataPoint.equalsIgnoreCase(MESSAGE) || dataPoint.equalsIgnoreCase(LogEngineConstants.EVENT_CATEGORY) ||
                        dataPoint.equalsIgnoreCase(LogEngineConstants.EVENT_SOURCE_TYPE) || dataPoint.equalsIgnoreCase(EVENT_SOURCE))
                    {
                        entities = EventSourceConfigStore.getStore().flatItems(EVENT_SOURCE);
                    }
                    else if (!eventColumns.isEmpty() && eventColumns.containsKey(dataPoint))
                    {
                        entities = EventSourceConfigStore.getStore().flatItemsByMultiValueFields(EVENT_TYPE,
                            value.getString(POLICY_TYPE).toLowerCase(), EVENT_SOURCE, PLUGIN_ID,
                            eventColumns.getJsonObject(dataPoint).getJsonArray(DatastoreConstants.MAPPER_PLUGIN_IDS));
                    }
                    else
                    {
                        entities = new JsonArray();
                    }

                    assignBySource(value.getLong(ID), dataPoint, entities, policiesBySource);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Finds applicable policies for an event based on its source, source type, and groups
     */
    private Map<String, Set<Long>> assign(JsonArray groups, String source, String eventSourceType)
    {
        var qualifiedPolicies = new HashMap<String, Set<Long>>();

        try
        {
            policiesBySource.getOrDefault(source, Collections.emptyMap()).forEach((key, policyIds) -> {

                if (!policyIds.isEmpty())
                {
                    qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                }
            });

            if (CommonUtil.isNotNullOrEmpty(eventSourceType))
            {
                policiesBySourceType.getOrDefault(eventSourceType, Collections.emptyMap()).forEach((key, policyIds) -> {

                    if (!policyIds.isEmpty())
                    {
                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }

            for (var index = 0; index < groups.size(); index++)
            {
                var group = groups.getLong(index);

                policiesByGroup.getOrDefault(group, Collections.emptyMap()).forEach((key, policyIds) -> {

                    if (!policyIds.isEmpty())
                    {
                        qualifiedPolicies.computeIfAbsent(key, value -> new LinkedHashSet<>()).addAll(policyIds);
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedPolicies;
    }

    /**
     * Assign policies by source
     */
    private void assignBySource(long policyId, String key, JsonArray entities, Map<String, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            entities.forEach(entity -> {

                var object = CommonUtil.getString(entity);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }
                else if (!policiesByEntity.get(object).containsKey(key))
                {
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(key).contains(policyId))
                {
                    policiesByEntity.get(object).get(key).add(policyId);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Assign policies by ID
     */
    private void assignById(long policyId, String key, JsonArray entities, Map<Long, Map<String, List<Long>>> policiesByEntity)
    {
        try
        {
            entities.forEach(entity -> {

                var object = CommonUtil.getLong(entity);

                if (!policiesByEntity.containsKey(object))
                {
                    policiesByEntity.put(object, new HashMap<>());
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }
                else if (!policiesByEntity.get(object).containsKey(key))
                {
                    policiesByEntity.get(object).put(key, new ArrayList<>());
                }

                if (!policiesByEntity.get(object).get(key).contains(policyId)) {
                    policiesByEntity.get(object).get(key).add(policyId);
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Optimized inner class for tracking and sorting group statistics in policy evaluation.
     * <p>
     * This class maintains the group identifier, sum of values, and count of events
     * for calculating aggregations like sum, average, and count. It also provides
     * static utility methods for efficient sorting of group statistics.
     */
    public static class GroupStat
    {
        /**
         * The group identifier
         */
        public final String group;

        /**
         * Sum of values for this group
         */
        public long sum;

        /**
         * Count of events for this group
         */
        public int count;

        /**
         * Constructs a new GroupStat for the specified group.
         *
         * @param group The group identifier
         */
        public GroupStat(String group) {
            this.group = group;
        }

        /**
         * Calculates the average value for this group.
         *
         * @return The average value, or 0 if count is 0
         */
        public long avg() {
            return count == 0 ? 0 : sum / count;
        }

        /**
         * Sorts a list of group statistics and keeps only the top N groups.
         * <p>
         * This method uses a priority queue to efficiently find the top N groups
         * based on the specified aggregator (sum, avg, or count) without sorting the entire list.
         *
         * @param values     The list of group statistics to sort
         * @param aggregator The aggregation type to use for sorting (sum, avg, or count)
         * @param topN       The number of top groups to keep
         */
        public static void sort(List<GroupStat> values, String aggregator, int topN)
        {
            Comparator<GroupStat> comparator;

            if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.SUM.getName()))
            {
                comparator = Comparator.comparingLong((GroupStat stat) -> stat.sum).reversed();
            }
            else if (aggregator.equalsIgnoreCase(DatastoreConstants.AggregationType.AVG.getName()))
            {
                comparator = Comparator.comparingLong(GroupStat::avg).reversed();
            }
            else
            {
                comparator = Comparator.comparingInt((GroupStat stat) -> stat.count).reversed();
            }

            // Use Priority Queue for optimal TopK performance
            sortTopK(values, comparator, topN);
        }

        /**
         * Uses a priority queue to efficiently find the top K elements.
         * <p>
         * This approach maintains a min-heap of size K, which significantly outperforms
         * traditional sorting for large datasets. The priority queue only keeps the K largest
         * elements seen so far, making it very memory efficient and fast.
         *
         * @param values     List of GroupStat objects to process
         * @param comparator Comparator to use for sorting
         * @param k          Number of top elements to keep
         */
        private static void sortTopK(List<GroupStat> values, Comparator<GroupStat> comparator, int k)
        {
            if (values.isEmpty() || k <= 0)
            {
                return;
            }

            if (values.size() <= k)
            {
                // If we have fewer elements than k, just sort the list
                values.sort(comparator);
                return;
            }

            // Create a min heap with the reverse of our comparator
            // This keeps the K largest elements with the smallest at the top for easy replacement
            var queue = new PriorityQueue<>(k, comparator.reversed());

            // Process each element
            for (var value : values)
            {
                if (queue.size() < k)
                {
                    // If we haven't reached capacity, add the element
                    queue.offer(value);
                }
                else if (comparator.compare(value, queue.peek()) > 0)
                {
                    // If this element is larger than the smallest in our heap, replace it
                    queue.poll();
                    queue.offer(value);
                }
            }

            // Clear the original list
            values.clear();

            // Convert heap to array for sorting
            var elements = queue.toArray(new GroupStat[0]);

            // Sort the result in the desired order
            Arrays.sort(elements, comparator);

            // Add the sorted elements back to the list
            Collections.addAll(values, elements);
        }
    }
}
