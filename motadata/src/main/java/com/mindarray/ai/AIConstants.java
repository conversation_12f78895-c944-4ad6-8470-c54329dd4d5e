package com.mindarray.ai;

import com.mindarray.Bootstrap;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.client.WebClient;

public class AIConstants
{
    private static final String KEY = "********************************************************************************************************************************************************************";

    private static final WebClient client = WebClient.create(Bootstrap.vertx());

    private static final Logger LOGGER = new Logger(AIConstants.class, "AI", "AI Constants");

    public static Future<JsonObject> generateLogParser(JsonObject context)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var prompt = """
                    Here i've provided you the logs from the different event sources
                    Now your job is to give me regex grouped by event source
                    where you've given raw logs to help you to generate efficient regex
                    Please generate a Java Compatible regex with no underscore in named groups
                    merge the regex as possible as you can by grouping logs with same type together
                    the generated regex should at least parse the given logs so check the given regex with the given logs
                    generate the output in the format '{"127.0.0.1":[{"logType" : "", "regex" : ""}]}'
                    Do not include explanations, formatting, or markdown — output the **JSON object only**
                    please find the attached logs in below json:
                    """;

            prompt = prompt + context.encodePrettily();

            LOGGER.info(prompt);

            var payload = new JsonObject()
                    .put("model", "gpt-4o")
                    .put("input", prompt);

            client.postAbs("https://api.openai.com/v1/responses")
                    .putHeader("Authorization", "Bearer " + KEY)
                    .putHeader("Content-Type", "application/json")
                    .sendJsonObject(payload, ar ->
                    {
                        try
                        {
                            if (ar.succeeded())
                            {
                                var response = ar.result();

                                var resp = response.bodyAsJsonObject();

                                var content = new JsonObject(resp.getJsonArray("output").getJsonObject(0).getJsonArray("content").getJsonObject(0).getString("text"));

                                LOGGER.info(content.encodePrettily());

                                promise.complete(content);
                            }
                            else
                            {
                                LOGGER.error(ar.cause());

                                promise.complete(new JsonObject());
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            promise.complete(new JsonObject());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return promise.future();
    }
}
