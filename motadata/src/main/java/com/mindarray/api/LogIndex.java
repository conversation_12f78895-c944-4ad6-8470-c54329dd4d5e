/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.store.LogIndexConfigStore;
import com.mindarray.util.Logger;
import io.vertx.ext.web.Router;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class LogIndex extends AbstractAPI
{
    // Log Index Constants - based on log-indexes.json schema
    public static final String LOG_INDEX_NAME = "log.index.name";
    public static final String LOG_INDEX_RETENTION_PERIOD = "log.index.retention.period";
    public static final String LOG_INDEX_RETENTION_FILTER = "log.index.retention.filter";
    public static final String LOG_INDEX_STATUS = "log.index.status";

    private static final Logger LOGGER = new Logger(LogIndex.class, MOTADATA_API, "Log Indexes API");

    public LogIndex()
    {
        super("log-indexes", LogIndexConfigStore.getStore(), new Logger(LogIndex.class, MOTADATA_API, "Log Indexes API"));
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
