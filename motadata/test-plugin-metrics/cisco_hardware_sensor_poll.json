{"**********": {"errors": [], "object.ip": "**********", "port": 161, "result": {"cisco.hardware.fan.sensor": [{"cisco.hardware.fan.sensor": "Switch 1 - FAN - T1 1", "cisco.hardware.fan.sensor.operational.state": "Up"}, {"cisco.hardware.fan.sensor": "Switch 1 - FAN - T1 2", "cisco.hardware.fan.sensor.operational.state": "Up"}, {"cisco.hardware.fan.sensor": "Switch 1 - FAN - T1 3", "cisco.hardware.fan.sensor.operational.state": "Up"}], "cisco.hardware.power.supply.sensor": [{"cisco.hardware.power.supply.sensor": "Switch 1 - Power Supply A", "cisco.hardware.power.supply.sensor.operational.state": "on"}, {"cisco.hardware.power.supply.sensor": "Switch 1 - Power Supply B", "cisco.hardware.power.supply.sensor.operational.state": "offEnvOther"}], "cisco.hardware.temperature.sensor": [{"cisco.hardware.temperature.sensor": "Te1/1/5 Module Temperature Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 318, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Switch 1 - Inlet Temp Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 23, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Switch 1 - Outlet Temp Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 29, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Te1/1/7 Module Temperature Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 303, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Switch 1 - HotSpot Temp Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 50, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Te1/1/1 Module Temperature Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 247, "cisco.hardware.temperature.sensor.state": "Ok"}, {"cisco.hardware.temperature.sensor": "Te1/1/3 Module Temperature Sensor", "cisco.hardware.temperature.sensor.reading.celsius": 236, "cisco.hardware.temperature.sensor.state": "Ok"}]}, "snmp.community": "public", "snmp.version": "v2c"}}